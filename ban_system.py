"""
نظام إدارة الحظر للبوت
يدير حظر المستخدمين والقنوات مع حفظ البيانات في ملف JSON
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import os

logger = logging.getLogger(__name__)


class BanManager:
    """مدير نظام الحظر للمستخدمين والقنوات"""
    
    def __init__(self, ban_file: str = "banned_data.json"):
        """
        تهيئة مدير الحظر
        
        Args:
            ban_file (str): مسار ملف حفظ بيانات الحظر
        """
        self.ban_file = ban_file
        self.ban_data = self._load_ban_data()
        
    def _load_ban_data(self) -> Dict:
        """تحميل بيانات الحظر من الملف"""
        try:
            if os.path.exists(self.ban_file):
                with open(self.ban_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # التأكد من وجود المفاتيح الأساسية
                    if "banned_users" not in data:
                        data["banned_users"] = {}
                    if "banned_channels" not in data:
                        data["banned_channels"] = {}
                    return data
            else:
                # إنشاء هيكل البيانات الافتراضي
                return {
                    "banned_users": {},
                    "banned_channels": {},
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الحظر: {e}")
            return {
                "banned_users": {},
                "banned_channels": {},
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def _save_ban_data(self) -> bool:
        """حفظ بيانات الحظر في الملف"""
        try:
            self.ban_data["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with open(self.ban_file, 'w', encoding='utf-8') as f:
                json.dump(self.ban_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات الحظر: {e}")
            return False
    
    def ban_user(self, user_id: int, username: str = None, first_name: str = None, 
                 reason: str = "غير محدد", banned_by: str = "مشرف") -> bool:
        """
        حظر مستخدم
        
        Args:
            user_id (int): معرف المستخدم
            username (str): اسم المستخدم
            first_name (str): الاسم الأول
            reason (str): سبب الحظر
            banned_by (str): من قام بالحظر
            
        Returns:
            bool: True إذا تم الحظر بنجاح
        """
        try:
            self.ban_data["banned_users"][str(user_id)] = {
                "user_id": user_id,
                "username": username or "غير معروف",
                "first_name": first_name or "غير معروف",
                "reason": reason,
                "banned_by": banned_by,
                "banned_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            success = self._save_ban_data()
            if success:
                logger.info(f"تم حظر المستخدم {user_id} ({username}) بواسطة {banned_by}")
            return success
            
        except Exception as e:
            logger.error(f"خطأ في حظر المستخدم {user_id}: {e}")
            return False
    
    def unban_user(self, user_id: int) -> bool:
        """
        إلغاء حظر مستخدم
        
        Args:
            user_id (int): معرف المستخدم
            
        Returns:
            bool: True إذا تم إلغاء الحظر بنجاح
        """
        try:
            user_key = str(user_id)
            if user_key in self.ban_data["banned_users"]:
                username = self.ban_data["banned_users"][user_key].get("username", "غير معروف")
                del self.ban_data["banned_users"][user_key]
                success = self._save_ban_data()
                if success:
                    logger.info(f"تم إلغاء حظر المستخدم {user_id} ({username})")
                return success
            return False
            
        except Exception as e:
            logger.error(f"خطأ في إلغاء حظر المستخدم {user_id}: {e}")
            return False
    
    def ban_channel(self, channel_username: str, channel_title: str = None, 
                    reason: str = "غير محدد", banned_by: str = "مشرف") -> bool:
        """
        حظر قناة
        
        Args:
            channel_username (str): اسم المستخدم للقناة
            channel_title (str): عنوان القناة
            reason (str): سبب الحظر
            banned_by (str): من قام بالحظر
            
        Returns:
            bool: True إذا تم الحظر بنجاح
        """
        try:
            # تنظيف اسم القناة من الرموز الإضافية
            clean_username = channel_username.replace("@", "").replace("https://t.me/", "")
            
            self.ban_data["banned_channels"][clean_username] = {
                "username": clean_username,
                "title": channel_title or "غير معروف",
                "reason": reason,
                "banned_by": banned_by,
                "banned_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            success = self._save_ban_data()
            if success:
                logger.info(f"تم حظر القناة {clean_username} بواسطة {banned_by}")
            return success
            
        except Exception as e:
            logger.error(f"خطأ في حظر القناة {channel_username}: {e}")
            return False
    
    def unban_channel(self, channel_username: str) -> bool:
        """
        إلغاء حظر قناة
        
        Args:
            channel_username (str): اسم المستخدم للقناة
            
        Returns:
            bool: True إذا تم إلغاء الحظر بنجاح
        """
        try:
            # تنظيف اسم القناة من الرموز الإضافية
            clean_username = channel_username.replace("@", "").replace("https://t.me/", "")
            
            if clean_username in self.ban_data["banned_channels"]:
                title = self.ban_data["banned_channels"][clean_username].get("title", "غير معروف")
                del self.ban_data["banned_channels"][clean_username]
                success = self._save_ban_data()
                if success:
                    logger.info(f"تم إلغاء حظر القناة {clean_username} ({title})")
                return success
            return False
            
        except Exception as e:
            logger.error(f"خطأ في إلغاء حظر القناة {channel_username}: {e}")
            return False
    
    def is_user_banned(self, user_id: int) -> bool:
        """
        التحقق من حظر المستخدم
        
        Args:
            user_id (int): معرف المستخدم
            
        Returns:
            bool: True إذا كان المستخدم محظور
        """
        return str(user_id) in self.ban_data["banned_users"]
    
    def is_channel_banned(self, channel_username: str) -> bool:
        """
        التحقق من حظر القناة
        
        Args:
            channel_username (str): اسم المستخدم للقناة
            
        Returns:
            bool: True إذا كانت القناة محظورة
        """
        # تنظيف اسم القناة من الرموز الإضافية
        clean_username = channel_username.replace("@", "").replace("https://t.me/", "")
        return clean_username in self.ban_data["banned_channels"]
    
    def get_banned_user_info(self, user_id: int) -> Optional[Dict]:
        """
        الحصول على معلومات المستخدم المحظور
        
        Args:
            user_id (int): معرف المستخدم
            
        Returns:
            Optional[Dict]: معلومات الحظر أو None
        """
        return self.ban_data["banned_users"].get(str(user_id))
    
    def get_banned_channel_info(self, channel_username: str) -> Optional[Dict]:
        """
        الحصول على معلومات القناة المحظورة
        
        Args:
            channel_username (str): اسم المستخدم للقناة
            
        Returns:
            Optional[Dict]: معلومات الحظر أو None
        """
        clean_username = channel_username.replace("@", "").replace("https://t.me/", "")
        return self.ban_data["banned_channels"].get(clean_username)
    
    def get_all_banned_users(self) -> Dict:
        """الحصول على جميع المستخدمين المحظورين"""
        return self.ban_data["banned_users"]
    
    def get_all_banned_channels(self) -> Dict:
        """الحصول على جميع القنوات المحظورة"""
        return self.ban_data["banned_channels"]
    
    def get_ban_statistics(self) -> Dict:
        """الحصول على إحصائيات الحظر"""
        return {
            "total_banned_users": len(self.ban_data["banned_users"]),
            "total_banned_channels": len(self.ban_data["banned_channels"]),
            "last_updated": self.ban_data.get("last_updated", "غير معروف")
        }
    
    def clear_all_bans(self) -> bool:
        """مسح جميع بيانات الحظر (للطوارئ فقط)"""
        try:
            self.ban_data["banned_users"] = {}
            self.ban_data["banned_channels"] = {}
            success = self._save_ban_data()
            if success:
                logger.warning("تم مسح جميع بيانات الحظر!")
            return success
        except Exception as e:
            logger.error(f"خطأ في مسح بيانات الحظر: {e}")
            return False


# إنشاء مثيل عام لمدير الحظر
ban_manager = BanManager()
