# 🛡️ دليل نظام فلترة المحتوى المتقدم

## 🎯 نظرة عامة

تم إضافة **نظام فلترة المحتوى المتقدم** للبوت لحمايته من إساءة الاستخدام والمحتوى غير المناسب. النظام يقوم بفلترة المحتوى الإباحي والمخالف تلقائياً ويحظر القنوات والمستخدمين المخالفين.

---

## ✨ الميزات الرئيسية

### 🚫 **فلترة تلقائية للمحتوى**
- **فحص النصوص**: فحص جميع النصوص للكلمات المحظورة
- **فحص القنوات**: فحص أسماء القنوات للمحتوى المشبوه
- **حظر تلقائي**: حظر القنوات المخالفة تلقائياً
- **تحذيرات**: إرسال رسائل تحذيرية بدلاً من المحتوى المحظور

### 🎯 **نظام ذكي للكشف**
- **كلمات محظورة**: قائمة شاملة بالكلمات الإباحية والمخالفة
- **كلمات مشبوهة**: كلمات تحتاج مراجعة إضافية
- **مستوى الثقة**: تقييم مستوى خطورة المحتوى
- **فلترة متعددة اللغات**: دعم العربية والإنجليزية

### 📊 **إحصائيات مفصلة**
- **عدد المحتوى المفحوص**: إجمالي المحتوى الذي تم فحصه
- **المحتوى المحظور**: عدد المحتوى المحظور
- **القنوات المحظورة**: عدد القنوات المحظورة تلقائياً
- **معدل الحظر**: نسبة المحتوى المحظور

---

## 🔧 الأوامر الإدارية

### **📊 عرض الإحصائيات**
```
/filter_stats
```
- عرض إحصائيات شاملة عن نظام الفلترة
- معدل الحظر والكشف
- عدد الكلمات المحظورة والمشبوهة

### **🚫 إضافة كلمة محظورة**
```
/add_blocked_word كلمة_محظورة
```
**مثال:**
```
/add_blocked_word محتوى_مخالف
```

### **🧪 اختبار الفلترة**
```
/test_filter النص المراد اختباره
```
**مثال:**
```
/test_filter هذا نص للاختبار
```

### **🚫 حظر قناة**
```
/block_channel اسم_القناة [سبب_الحظر]
```
**مثال:**
```
/block_channel bad_channel محتوى إباحي مخالف
```

### **✅ إلغاء حظر قناة**
```
/unblock_channel اسم_القناة
```
**مثال:**
```
/unblock_channel good_channel
```

---

## 🛠️ كيفية عمل النظام

### **1. فحص المحتوى**
```python
# عند استقبال أي محتوى
content_result = content_filter.filter_message(text, channel)

if content_result.is_blocked:
    # حظر المحتوى وإرسال تحذير
    send_warning_message()
else:
    # السماح بالمحتوى
    send_content()
```

### **2. الحظر التلقائي**
- **مستوى ثقة عالي (>0.8)**: حظر القناة تلقائياً
- **مستوى ثقة متوسط (0.5-0.8)**: تحذير وحظر المحتوى
- **مستوى ثقة منخفض (<0.5)**: السماح بالمحتوى

### **3. أنواع الكلمات المحظورة**

#### **كلمات محظورة تماماً:**
- كلمات إباحية صريحة
- محتوى جنسي مباشر
- مصطلحات مواقع إباحية
- رموز وإيموجي مخالفة

#### **كلمات مشبوهة:**
- كلمات قد تكون مخالفة حسب السياق
- تحتاج عدة كلمات مشبوهة لتفعيل الحظر
- تستخدم للكشف المبكر

---

## 📋 قائمة الكلمات المحظورة

### **🔴 كلمات عربية محظورة:**
- سكس، جنس، عاري، عارية
- إباحي، إباحية، فاضح، فاضحة
- مثير، مثيرة، ساخن، ساخنة
- شهوة، شهواني، جنسي، جنسية
- للكبار فقط، محتوى للبالغين

### **🔴 كلمات إنجليزية محظورة:**
- porn, sex, nude, naked
- adult, xxx, nsfw, erotic
- sexy, hot, horny
- مصطلحات جنسية صريحة
- أسماء مواقع إباحية

### **⚠️ كلمات مشبوهة:**
- حبيبي، حبيبتي، عزيزي، عزيزتي
- صور شخصية، فيديو شخصي
- خاص، سري، مخفي
- تعارف، صداقة، علاقة

---

## 🎛️ الإعدادات المتقدمة

### **تخصيص قائمة الكلمات:**
```json
{
  "blocked_keywords": [
    "كلمة_محظورة_1",
    "كلمة_محظورة_2"
  ],
  "suspicious_keywords": [
    "كلمة_مشبوهة_1",
    "كلمة_مشبوهة_2"
  ]
}
```

### **إدارة القنوات:**
```json
{
  "blocked_channels": [
    "قناة_محظورة_1",
    "قناة_محظورة_2"
  ],
  "whitelist_channels": [
    "قناة_مسموحة_1",
    "قناة_مسموحة_2"
  ]
}
```

---

## 📊 مثال على الإحصائيات

```
📊 إحصائيات فلترة المحتوى

🔍 المحتوى المفحوص: 1,234
🚫 المحتوى المحظور: 45
📺 القنوات المحظورة: 12

📈 معدل الحظر: 3.65%

🔧 الكلمات المحظورة: 67
⚠️ الكلمات المشبوهة: 28

📅 آخر تحديث: 2025-08-02 12:30:45
```

---

## 🚨 رسائل التحذير

### **محتوى محظور:**
```
🚫 تم حظر هذا المحتوى

📝 السبب: محتوى إباحي مخالف
⚖️ سياسة البوت: لا يسمح بالمحتوى غير المناسب

💡 ملاحظة: البوت يحمي المستخدمين من المحتوى المخالف
```

### **قناة محظورة:**
```
🚫 تم حظر هذه القناة تلقائياً

📺 القناة: @example_channel
📝 السبب: محتوى غير مناسب
🎯 مستوى الثقة: 0.95

⚖️ سياسة البوت: لا يسمح بسحب المحتوى غير المناسب
```

---

## 🔄 الصيانة والتحديث

### **مراقبة دورية:**
- فحص الإحصائيات يومياً
- مراجعة القنوات المحظورة أسبوعياً
- تحديث قائمة الكلمات شهرياً

### **تحسين النظام:**
- إضافة كلمات جديدة حسب الحاجة
- تعديل مستويات الثقة
- تحسين دقة الكشف

### **النسخ الاحتياطية:**
- حفظ إعدادات الفلترة
- نسخ احتياطية من الإحصائيات
- حفظ قوائم القنوات المحظورة

---

## 🎯 أفضل الممارسات

### **للمشرفين:**
1. **مراجعة دورية**: فحص الإحصائيات بانتظام
2. **تحديث القوائم**: إضافة كلمات جديدة عند الحاجة
3. **مراقبة الأداء**: متابعة معدل الحظر والدقة
4. **التواصل**: الرد على استفسارات المستخدمين

### **للمستخدمين:**
1. **احترام السياسات**: تجنب المحتوى المخالف
2. **الإبلاغ**: إبلاغ المشرفين عن المحتوى المشبوه
3. **التفهم**: تقبل قرارات النظام التلقائية
4. **التواصل**: التواصل مع المطور للاستفسارات

---

## 📞 الدعم والمساعدة

### **للاستفسارات:**
- **المطور**: [@GurusVIP](https://t.me/GurusVIP)
- **الدعم الفني**: متاح 24/7
- **التحديثات**: تابع للحصول على التحسينات

### **الإبلاغ عن مشاكل:**
- محتوى مخالف لم يتم حظره
- محتوى مناسب تم حظره خطأً
- مشاكل في الأداء أو الأخطاء

---

## 🎉 الخلاصة

**نظام فلترة المحتوى المتقدم** يوفر حماية شاملة للبوت من إساءة الاستخدام:

✅ **فلترة تلقائية** للمحتوى المخالف
✅ **حظر ذكي** للقنوات المشبوهة  
✅ **إحصائيات مفصلة** للمراقبة
✅ **أوامر إدارية** سهلة الاستخدام
✅ **حماية متعددة المستويات** للمستخدمين

النظام يعمل بشكل تلقائي ولا يحتاج تدخل مستمر، مما يضمن بيئة آمنة ومناسبة لجميع المستخدمين.
