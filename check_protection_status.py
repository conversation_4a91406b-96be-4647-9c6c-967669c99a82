#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت فحص حالة الحماية
يتحقق من فعالية جميع أنظمة الحماية المطبقة
"""

import sys
import json
from datetime import datetime

def check_protection_status():
    """فحص شامل لحالة الحماية"""
    
    print("🛡️ فحص حالة الحماية الشاملة")
    print("=" * 60)
    
    try:
        from ban_system import BanManager
        from content_filter import ContentFilter
        
        ban_manager = BanManager()
        content_filter = ContentFilter()
        
        print("✅ تم تحميل أنظمة الحماية بنجاح\n")
        
        # 1. فحص حظر المستخدم المخالف
        print("👤 فحص حظر المستخدم المخالف:")
        print("-" * 40)
        
        abusive_user_id = 5018142889
        user_banned = ban_manager.is_user_banned(abusive_user_id)
        
        if user_banned:
            print(f"✅ المستخدم {abusive_user_id} محظور بنجاح")
            
            print(f"   📝 السبب: استغلال البوت لسحب محتوى إباحي مخالف")
            print(f"   📅 تاريخ الحظر: 2025-08-02")
            print(f"   👮 محظور بواسطة: نظام الحماية التلقائي")
        else:
            print(f"❌ المستخدم {abusive_user_id} غير محظور!")
        
        # 2. فحص حظر القنوات المخالفة
        print(f"\n📺 فحص حظر القنوات المخالفة:")
        print("-" * 40)
        
        # قائمة القنوات المخالفة للفحص
        test_channels = [
            "pornhub_onlyfans_top",
            "xxxeroge", 
            "brzervip_2024",
            "hotguyspremiumchannel",
            "avom001",
            "oumei18m",
            "japanfc22",
            "canale9mq"
        ]
        
        banned_count = 0
        total_channels = len(test_channels)
        
        for channel in test_channels:
            # فحص نظام الحظر العادي
            ban_status = ban_manager.is_channel_banned(channel)
            
            # فحص نظام الفلترة
            filter_result = content_filter.check_channel_content(channel)
            filter_status = filter_result.is_blocked
            
            if ban_status and filter_status:
                print(f"✅ @{channel} - محظورة في جميع الأنظمة")
                banned_count += 1
            elif ban_status:
                print(f"⚠️ @{channel} - محظورة في نظام الحظر فقط")
            elif filter_status:
                print(f"⚠️ @{channel} - محظورة في نظام الفلترة فقط")
            else:
                print(f"❌ @{channel} - غير محظورة!")
        
        print(f"\n📊 إحصائيات حظر القنوات:")
        print(f"   ✅ محظورة بالكامل: {banned_count}/{total_channels}")
        print(f"   📈 معدل الحماية: {(banned_count/total_channels*100):.1f}%")
        
        # 3. فحص نظام الفلترة
        print(f"\n🔍 فحص نظام الفلترة:")
        print("-" * 40)
        
        stats = content_filter.get_stats()

        # قراءة الإعدادات من الملف مباشرة
        try:
            with open("content_filter_config.json", "r", encoding="utf-8") as f:
                config = json.load(f)
        except:
            config = {}
        
        print(f"   🚫 الكلمات المحظورة: {len(config.get('blocked_keywords', []))}")
        print(f"   ⚠️ الكلمات المشبوهة: {len(config.get('suspicious_keywords', []))}")
        print(f"   📺 القنوات المحظورة: {len(config.get('blocked_channels', []))}")
        print(f"   ✅ القنوات المسموحة: {len(config.get('whitelist_channels', []))}")
        
        print(f"\n   📊 إحصائيات الاستخدام:")
        print(f"   🔍 المحتوى المفحوص: {stats.get('total_checked', 0)}")
        print(f"   🚫 المحتوى المحظور: {stats.get('blocked_content', 0)}")
        print(f"   📺 القنوات المحظورة تلقائياً: {stats.get('blocked_channels', 0)}")
        
        # 4. اختبار فعالية الفلترة
        print(f"\n🧪 اختبار فعالية الفلترة:")
        print("-" * 40)
        
        # نصوص اختبار
        test_cases = [
            {
                "text": "محتوى إباحي فاضح xxx",
                "should_block": True,
                "category": "محتوى محظور صريح"
            },
            {
                "text": "قناة pornhub للكبار فقط", 
                "should_block": True,
                "category": "اسم موقع إباحي"
            },
            {
                "text": "فيديو من brzervip مثير",
                "should_block": True,
                "category": "اسم قناة محظورة"
            },
            {
                "text": "معلومات تقنية مفيدة",
                "should_block": False,
                "category": "محتوى عادي"
            },
            {
                "text": "شرح جميل ومفيد",
                "should_block": False,
                "category": "محتوى إيجابي"
            }
        ]
        
        correct_detections = 0
        total_tests = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            result = content_filter.filter_message(test_case["text"])
            is_blocked = result.is_blocked
            should_block = test_case["should_block"]
            
            if is_blocked == should_block:
                status = "✅ صحيح"
                correct_detections += 1
            else:
                status = "❌ خطأ"
            
            print(f"   {i}. {status} | {test_case['category']}")
            print(f"      النص: {test_case['text']}")
            print(f"      النتيجة: {'محظور' if is_blocked else 'مسموح'}")
            
            if is_blocked and result.reason:
                print(f"      السبب: {result.reason}")
                print(f"      الثقة: {result.confidence:.2f}")
        
        print(f"\n   📊 دقة الكشف: {(correct_detections/total_tests*100):.1f}%")
        
        # 5. فحص القنوات المحظورة حديثاً
        print(f"\n🆕 القنوات المحظورة حديثاً:")
        print("-" * 40)
        
        recent_channels = config.get('blocked_channels', [])[-10:]  # آخر 10 قنوات
        
        for channel in recent_channels:
            filter_result = content_filter.check_channel_content(channel)
            print(f"   🚫 @{channel} - {filter_result.reason if filter_result.is_blocked else 'محظورة'}")
        
        # 6. تقييم الحماية الشاملة
        print(f"\n🎯 تقييم الحماية الشاملة:")
        print("-" * 40)
        
        protection_score = 0
        max_score = 100
        
        # تقييم حظر المستخدم (20 نقطة)
        if user_banned:
            protection_score += 20
            print("   ✅ حظر المستخدم المخالف: 20/20")
        else:
            print("   ❌ حظر المستخدم المخالف: 0/20")
        
        # تقييم حظر القنوات (30 نقطة)
        channel_score = int((banned_count/total_channels) * 30)
        protection_score += channel_score
        print(f"   {'✅' if channel_score >= 25 else '⚠️'} حظر القنوات المخالفة: {channel_score}/30")
        
        # تقييم نظام الفلترة (25 نقطة)
        filter_score = int((correct_detections/total_tests) * 25)
        protection_score += filter_score
        print(f"   {'✅' if filter_score >= 20 else '⚠️'} دقة نظام الفلترة: {filter_score}/25")
        
        # تقييم شمولية الكلمات المحظورة (25 نقطة)
        keyword_count = len(config.get('blocked_keywords', []))
        if keyword_count >= 100:
            keyword_score = 25
        elif keyword_count >= 75:
            keyword_score = 20
        elif keyword_count >= 50:
            keyword_score = 15
        else:
            keyword_score = 10
        
        protection_score += keyword_score
        print(f"   {'✅' if keyword_score >= 20 else '⚠️'} شمولية الكلمات المحظورة: {keyword_score}/25")
        
        # النتيجة النهائية
        print(f"\n🏆 النتيجة النهائية: {protection_score}/100")
        
        if protection_score >= 90:
            grade = "ممتاز 🥇"
            status = "الحماية في أعلى مستوياتها"
        elif protection_score >= 80:
            grade = "جيد جداً 🥈"
            status = "الحماية قوية ومناسبة"
        elif protection_score >= 70:
            grade = "جيد 🥉"
            status = "الحماية مقبولة مع حاجة لتحسين"
        else:
            grade = "يحتاج تحسين ⚠️"
            status = "الحماية ضعيفة وتحتاج تطوير"
        
        print(f"   🎖️ التقدير: {grade}")
        print(f"   📝 الحالة: {status}")
        
        # إنشاء تقرير الحماية
        protection_report = {
            "timestamp": datetime.now().isoformat(),
            "protection_score": protection_score,
            "grade": grade,
            "status": status,
            "details": {
                "user_banned": user_banned,
                "channels_protection_rate": f"{(banned_count/total_channels*100):.1f}%",
                "filter_accuracy": f"{(correct_detections/total_tests*100):.1f}%",
                "blocked_keywords": len(config.get('blocked_keywords', [])),
                "blocked_channels": len(config.get('blocked_channels', [])),
                "total_checked": stats.get('total_checked', 0),
                "blocked_content": stats.get('blocked_content', 0)
            }
        }
        
        # حفظ تقرير الحماية
        with open("protection_status_report.json", "w", encoding="utf-8") as f:
            json.dump(protection_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تقرير الحماية في: protection_status_report.json")
        
        return protection_score >= 80
        
    except Exception as e:
        print(f"❌ خطأ في فحص الحماية: {e}")
        return False

if __name__ == "__main__":
    try:
        success = check_protection_status()
        
        if success:
            print(f"\n🎉 الحماية تعمل بكفاءة عالية!")
            print(f"🛡️ البوت محمي بالكامل من الاستغلال")
            sys.exit(0)
        else:
            print(f"\n⚠️ الحماية تحتاج تحسين!")
            print(f"🔧 يُنصح بمراجعة الإعدادات")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 خطأ في تشغيل فحص الحماية: {e}")
        sys.exit(1)
