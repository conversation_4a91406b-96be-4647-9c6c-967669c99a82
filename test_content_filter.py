#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت اختبار نظام فلترة المحتوى
يقوم بفحص النظام والتأكد من عمله بشكل صحيح
"""

import sys
import json
from datetime import datetime
from content_filter import ContentFilter

def test_content_filter():
    """اختبار شامل لنظام فلترة المحتوى"""
    
    print("🧪 بدء اختبار نظام فلترة المحتوى")
    print("=" * 60)
    
    # إنشاء مثيل من الفلتر
    filter_system = ContentFilter()
    
    # اختبارات النصوص
    test_cases = [
        # نصوص مناسبة
        {
            "text": "مرحبا كيف الحال؟",
            "expected": False,
            "category": "نص عادي"
        },
        {
            "text": "شرح مفيد وجميل",
            "expected": False,
            "category": "نص إيجابي"
        },
        {
            "text": "معلومات تقنية مهمة",
            "expected": False,
            "category": "محتوى تعليمي"
        },
        
        # نصوص محظورة
        {
            "text": "محتوى إباحي فاضح",
            "expected": True,
            "category": "محتوى محظور"
        },
        {
            "text": "صور ساخنة للكبار فقط",
            "expected": True,
            "category": "محتوى محظور"
        },
        {
            "text": "فيديو سكس مثير",
            "expected": True,
            "category": "محتوى محظور"
        },
        {
            "text": "porn videos xxx",
            "expected": True,
            "category": "محتوى محظور إنجليزي"
        },
        
        # نصوص مشبوهة
        {
            "text": "تعارف وصداقة جميلة حبيبتي",
            "expected": True,
            "category": "محتوى مشبوه"
        },
        {
            "text": "صور شخصية خاصة سرية",
            "expected": True,
            "category": "محتوى مشبوه"
        },
        
        # نصوص حدية
        {
            "text": "حبيبي كيف الحال",
            "expected": False,
            "category": "نص حدي"
        },
        {
            "text": "صور جميلة",
            "expected": False,
            "category": "نص حدي"
        }
    ]
    
    # تشغيل الاختبارات
    passed = 0
    failed = 0
    
    print("🔍 اختبار فلترة النصوص:")
    print("-" * 40)
    
    for i, test_case in enumerate(test_cases, 1):
        text = test_case["text"]
        expected = test_case["expected"]
        category = test_case["category"]
        
        result = filter_system.filter_message(text)
        actual = result.is_blocked
        
        status = "✅ نجح" if actual == expected else "❌ فشل"
        
        print(f"{i:2d}. {status} | {category}")
        print(f"    النص: {text}")
        print(f"    متوقع: {'محظور' if expected else 'مسموح'}")
        print(f"    فعلي: {'محظور' if actual else 'مسموح'}")
        
        if actual and result.reason:
            print(f"    السبب: {result.reason}")
            print(f"    الثقة: {result.confidence:.2f}")
        
        if actual == expected:
            passed += 1
        else:
            failed += 1
        
        print()
    
    # اختبار القنوات
    print("📺 اختبار فلترة القنوات:")
    print("-" * 40)
    
    channel_tests = [
        {
            "channel": "news_channel",
            "expected": False,
            "category": "قناة عادية"
        },
        {
            "channel": "tech_updates",
            "expected": False,
            "category": "قناة تقنية"
        },
        {
            "channel": "sexy_girls",
            "expected": True,
            "category": "قناة مشبوهة"
        },
        {
            "channel": "porn_videos",
            "expected": True,
            "category": "قناة محظورة"
        }
    ]
    
    for i, test_case in enumerate(channel_tests, 1):
        channel = test_case["channel"]
        expected = test_case["expected"]
        category = test_case["category"]
        
        result = filter_system.check_channel_content(channel)
        actual = result.is_blocked
        
        status = "✅ نجح" if actual == expected else "❌ فشل"
        
        print(f"{i}. {status} | {category}")
        print(f"   القناة: @{channel}")
        print(f"   متوقع: {'محظورة' if expected else 'مسموحة'}")
        print(f"   فعلي: {'محظورة' if actual else 'مسموحة'}")
        
        if actual and result.reason:
            print(f"   السبب: {result.reason}")
        
        if actual == expected:
            passed += 1
        else:
            failed += 1
        
        print()
    
    # اختبار إدارة الكلمات
    print("🔧 اختبار إدارة الكلمات:")
    print("-" * 40)
    
    # إضافة كلمة جديدة
    test_keyword = "كلمة_اختبار"
    filter_system.add_keyword(test_keyword, is_blocked=True)
    
    # اختبار الكلمة الجديدة
    test_result = filter_system.filter_message(f"هذا نص يحتوي على {test_keyword}")
    
    if test_result.is_blocked:
        print("✅ نجح اختبار إضافة كلمة محظورة")
        passed += 1
    else:
        print("❌ فشل اختبار إضافة كلمة محظورة")
        failed += 1
    
    # إزالة الكلمة
    filter_system.remove_keyword(test_keyword, is_blocked=True)
    
    # اختبار إزالة الكلمة
    test_result2 = filter_system.filter_message(f"هذا نص يحتوي على {test_keyword}")
    
    if not test_result2.is_blocked:
        print("✅ نجح اختبار إزالة كلمة محظورة")
        passed += 1
    else:
        print("❌ فشل اختبار إزالة كلمة محظورة")
        failed += 1
    
    # اختبار إدارة القنوات
    print("\n📺 اختبار إدارة القنوات:")
    print("-" * 40)
    
    test_channel = "test_channel_123"
    
    # إضافة قناة للحظر
    filter_system.add_blocked_channel(test_channel, "اختبار")
    
    # اختبار حظر القناة
    channel_result = filter_system.check_channel_content(test_channel)
    
    if channel_result.is_blocked:
        print("✅ نجح اختبار حظر القناة")
        passed += 1
    else:
        print("❌ فشل اختبار حظر القناة")
        failed += 1
    
    # إزالة القناة من الحظر
    filter_system.remove_blocked_channel(test_channel)
    
    # اختبار إزالة الحظر
    channel_result2 = filter_system.check_channel_content(test_channel)
    
    if not channel_result2.is_blocked:
        print("✅ نجح اختبار إزالة حظر القناة")
        passed += 1
    else:
        print("❌ فشل اختبار إزالة حظر القناة")
        failed += 1
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed / (passed + failed) * 100):.1f}%")
    
    # عرض الإحصائيات
    stats = filter_system.get_stats()
    print(f"\n📊 إحصائيات النظام:")
    print(f"🔍 المحتوى المفحوص: {stats['total_checked']}")
    print(f"🚫 المحتوى المحظور: {stats['blocked_content']}")
    print(f"📺 القنوات المحظورة: {stats['blocked_channels']}")
    
    # تقييم الأداء
    if failed == 0:
        print("\n🎉 ممتاز! جميع الاختبارات نجحت")
        return True
    elif failed <= 2:
        print("\n⚠️ جيد! معظم الاختبارات نجحت")
        return True
    else:
        print("\n❌ يحتاج تحسين! عدة اختبارات فشلت")
        return False

def test_performance():
    """اختبار أداء النظام"""
    
    print("\n⚡ اختبار الأداء:")
    print("-" * 40)
    
    filter_system = ContentFilter()
    
    # نصوص للاختبار
    test_texts = [
        "نص عادي للاختبار",
        "محتوى إباحي فاضح",
        "معلومات مفيدة وجميلة",
        "صور ساخنة xxx",
        "شرح تقني متقدم"
    ] * 100  # تكرار للاختبار
    
    start_time = datetime.now()
    
    for text in test_texts:
        filter_system.filter_message(text)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print(f"⏱️ الوقت المستغرق: {duration:.3f} ثانية")
    print(f"🚀 السرعة: {len(test_texts)/duration:.1f} نص/ثانية")
    
    if duration < 1.0:
        print("✅ الأداء ممتاز")
    elif duration < 3.0:
        print("⚠️ الأداء جيد")
    else:
        print("❌ الأداء يحتاج تحسين")

if __name__ == "__main__":
    try:
        # تشغيل اختبار الوظائف
        success = test_content_filter()
        
        # تشغيل اختبار الأداء
        test_performance()
        
        # النتيجة النهائية
        if success:
            print("\n🎯 النظام جاهز للاستخدام!")
            sys.exit(0)
        else:
            print("\n⚠️ النظام يحتاج مراجعة!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        sys.exit(1)
