#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت الحظر الطارئ للمستخدمين والقنوات المخالفة
يقوم بحظر المستخدم المخالف وجميع القنوات الإباحية المذكورة
"""

import sys
import json
from datetime import datetime

def emergency_ban():
    """حظر طارئ للمستخدم والقنوات المخالفة"""
    
    print("🚨 بدء الحظر الطارئ للمحتوى المخالف")
    print("=" * 60)
    
    # معلومات المستخدم المخالف
    abusive_user = {
        "user_id": 5018142889,
        "username": "لا يوجد", 
        "first_name": ".",
        "reason": "استغلال البوت لسحب محتوى إباحي مخالف بشكل مكثف",
        "evidence": "سحب أكثر من 40 رابط إباحي في جلسة واحدة"
    }
    
    # القنوات الإباحية المخالفة
    abusive_channels = [
        "eeeooop",
        "xxxeroge", 
        "ty2028av",
        "oumei51",
        "mdmdmdmd6677",
        "teletop123bott1",
        "omavjx",
        "clipnong24h",
        "avom001",
        "avkankan1",
        "pornhub_onlyfans_top",
        "saselk",
        "brzervip_2024",
        "flighthk18",
        "linkbestallj",
        "sdcfk",
        "chengdu_i4",
        "yindaoom",
        "bopainglink",
        "hotguyspremiumchannel",
        "ocjavun",
        "dymax2025",
        "oumei18m",
        "flouropz",
        "jpbhjs",
        "avgc9",
        "dssaza9999",
        "canale9mq",
        "dualipagiornaliera",
        "zhongwenbao_q5",
        "muzi_u5",
        "avtvbbv",
        "ocxxmmsub",
        "omsqyp",
        "msjxhshk",
        "japanfc22",
        "mymmalay"
    ]
    
    try:
        # استيراد أنظمة الحظر
        from ban_system import BanManager
        from content_filter import ContentFilter
        
        ban_manager = BanManager()
        content_filter = ContentFilter()
        
        print("✅ تم تحميل أنظمة الحظر بنجاح")
        
        # حظر المستخدم المخالف
        print(f"\n🚫 حظر المستخدم المخالف:")
        print(f"   المعرف: {abusive_user['user_id']}")
        print(f"   الاسم: {abusive_user['first_name']}")
        print(f"   السبب: {abusive_user['reason']}")
        
        success = ban_manager.ban_user(
            user_id=abusive_user['user_id'],
            username=abusive_user['username'],
            first_name=abusive_user['first_name'],
            reason=abusive_user['reason'],
            banned_by="نظام الحماية التلقائي"
        )
        
        if success:
            print("   ✅ تم حظر المستخدم بنجاح")
        else:
            print("   ❌ فشل في حظر المستخدم")
        
        # حظر جميع القنوات المخالفة
        print(f"\n📺 حظر القنوات المخالفة ({len(abusive_channels)} قناة):")
        
        banned_channels = 0
        failed_channels = 0
        
        for channel in abusive_channels:
            print(f"   🚫 حظر قناة: @{channel}")
            
            # حظر في نظام الحظر العادي
            ban_success = ban_manager.ban_channel(
                channel_username=channel,
                reason="قناة إباحية مخالفة - محتوى غير مناسب",
                banned_by="نظام الحماية التلقائي"
            )
            
            # حظر في نظام فلترة المحتوى
            content_filter.add_blocked_channel(
                channel, 
                "قناة إباحية مخالفة - تم اكتشافها من خلال الاستخدام المسيء"
            )
            
            if ban_success:
                banned_channels += 1
                print(f"      ✅ تم الحظر")
            else:
                failed_channels += 1
                print(f"      ❌ فشل الحظر")
        
        # إحصائيات الحظر
        print(f"\n📊 إحصائيات الحظر:")
        print(f"   ✅ قنوات محظورة: {banned_channels}")
        print(f"   ❌ قنوات فشل حظرها: {failed_channels}")
        print(f"   📈 معدل النجاح: {(banned_channels/len(abusive_channels)*100):.1f}%")
        
        # تحديث إحصائيات نظام الفلترة
        stats = content_filter.get_stats()
        print(f"\n📊 إحصائيات نظام الفلترة المحدثة:")
        print(f"   🚫 القنوات المحظورة: {len(abusive_channels)}")
        print(f"   🔍 إجمالي المحتوى المفحوص: {stats.get('total_checked', 0)}")
        print(f"   🚫 المحتوى المحظور: {stats.get('blocked_content', 0)}")
        
        # إنشاء تقرير الحظر
        ban_report = {
            "timestamp": datetime.now().isoformat(),
            "action": "emergency_ban",
            "banned_user": abusive_user,
            "banned_channels": abusive_channels,
            "results": {
                "user_banned": success,
                "channels_banned": banned_channels,
                "channels_failed": failed_channels,
                "success_rate": f"{(banned_channels/len(abusive_channels)*100):.1f}%"
            }
        }
        
        # حفظ تقرير الحظر
        with open("emergency_ban_report.json", "w", encoding="utf-8") as f:
            json.dump(ban_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تقرير الحظر في: emergency_ban_report.json")
        
        # رسالة تأكيد نهائية
        print(f"\n🎯 تم تنفيذ الحظر الطارئ بنجاح!")
        print(f"   🚫 المستخدم المخالف محظور")
        print(f"   📺 {banned_channels} قناة إباحية محظورة")
        print(f"   🛡️ البوت محمي من الاستغلال")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الأنظمة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تنفيذ الحظر: {e}")
        return False

def add_emergency_keywords():
    """إضافة كلمات طارئة للفلترة بناءً على أسماء القنوات المخالفة"""
    
    print("\n🔧 إضافة كلمات طارئة للفلترة:")
    print("-" * 40)
    
    # كلمات مستخرجة من أسماء القنوات المخالفة
    emergency_keywords = [
        "xxx", "porn", "av", "sex", "hot", "adult", "18+", "nsfw",
        "eroge", "hentai", "xxx", "pornhub", "onlyfans", "brzer",
        "clip", "nong", "sexy", "premium", "vip", "channel"
    ]
    
    try:
        from content_filter import ContentFilter
        content_filter = ContentFilter()
        
        added_count = 0
        for keyword in emergency_keywords:
            try:
                content_filter.add_keyword(keyword, is_blocked=True)
                print(f"   ✅ أضيفت كلمة: {keyword}")
                added_count += 1
            except Exception as e:
                print(f"   ❌ فشل إضافة كلمة {keyword}: {e}")
        
        print(f"\n📊 تم إضافة {added_count} كلمة جديدة للفلترة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الكلمات: {e}")
        return False

def verify_ban_status():
    """التحقق من حالة الحظر"""
    
    print("\n🔍 التحقق من حالة الحظر:")
    print("-" * 40)
    
    try:
        from ban_system import BanManager
        from content_filter import ContentFilter
        
        ban_manager = BanManager()
        content_filter = ContentFilter()
        
        # التحقق من حظر المستخدم
        user_banned = ban_manager.is_user_banned(5018142889)
        print(f"   المستخدم 5018142889: {'محظور ✅' if user_banned else 'غير محظور ❌'}")
        
        # التحقق من بعض القنوات
        test_channels = ["pornhub_onlyfans_top", "xxxeroge", "avom001"]
        
        for channel in test_channels:
            channel_banned = ban_manager.is_channel_banned(channel)
            filter_blocked = content_filter.check_channel_content(channel).is_blocked
            
            print(f"   @{channel}:")
            print(f"      نظام الحظر: {'محظورة ✅' if channel_banned else 'غير محظورة ❌'}")
            print(f"      نظام الفلترة: {'محظورة ✅' if filter_blocked else 'غير محظورة ❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

if __name__ == "__main__":
    try:
        print("🚨 تنفيذ الحظر الطارئ للمحتوى الإباحي المخالف")
        print("=" * 60)
        
        # تنفيذ الحظر الطارئ
        ban_success = emergency_ban()
        
        if ban_success:
            # إضافة كلمات طارئة
            add_emergency_keywords()
            
            # التحقق من الحظر
            verify_ban_status()
            
            print("\n🎉 تم تنفيذ جميع إجراءات الحماية بنجاح!")
            print("🛡️ البوت الآن محمي بالكامل من الاستغلال")
            
            sys.exit(0)
        else:
            print("\n❌ فشل في تنفيذ بعض إجراءات الحماية")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 خطأ عام في السكريبت: {e}")
        sys.exit(1)
