#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام فلترة المحتوى المتقدم
يقوم بفلترة المحتوى غير المناسب والإباحي
"""

import re
import json
import logging
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FilterResult:
    """نتيجة فلترة المحتوى"""
    is_blocked: bool
    reason: str
    confidence: float
    matched_keywords: List[str]

class ContentFilter:
    """نظام فلترة المحتوى المتقدم"""
    
    def __init__(self, config_file: str = "content_filter_config.json"):
        self.config_file = config_file
        self.blocked_keywords = []
        self.suspicious_keywords = []
        self.blocked_channels = set()
        self.whitelist_channels = set()
        self.filter_stats = {
            "total_checked": 0,
            "blocked_content": 0,
            "blocked_channels": 0,
            "last_updated": datetime.now().isoformat()
        }
        self.load_config()
    
    def load_config(self):
        """تحميل إعدادات الفلترة"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            self.blocked_keywords = config.get("blocked_keywords", [])
            self.suspicious_keywords = config.get("suspicious_keywords", [])
            self.blocked_channels = set(config.get("blocked_channels", []))
            self.whitelist_channels = set(config.get("whitelist_channels", []))
            self.filter_stats = config.get("filter_stats", self.filter_stats)
            
            logger.info(f"✅ تم تحميل إعدادات الفلترة: {len(self.blocked_keywords)} كلمة محظورة")
            
        except FileNotFoundError:
            logger.warning("⚠️ ملف الإعدادات غير موجود، سيتم إنشاء الإعدادات الافتراضية")
            self.create_default_config()
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل إعدادات الفلترة: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """إنشاء إعدادات افتراضية للفلترة"""
        
        # كلمات محظورة (محتوى إباحي وغير مناسب)
        self.blocked_keywords = [
            # كلمات إباحية عربية
            "سكس", "جنس", "عاري", "عارية", "إباحي", "إباحية", "فاضح", "فاضحة",
            "مثير", "مثيرة", "ساخن", "ساخنة", "شهوة", "شهواني", "جنسي", "جنسية",
            "عري", "تعري", "متعري", "متعرية", "فيديو ساخن", "صور ساخنة",
            "للكبار فقط", "محتوى للبالغين", "أفلام إباحية", "مقاطع إباحية",
            
            # كلمات إباحية إنجليزية
            "porn", "sex", "nude", "naked", "adult", "xxx", "nsfw", "erotic",
            "sexy", "hot", "horny", "fuck", "dick", "pussy", "boobs", "ass",
            "anal", "oral", "masturbation", "orgasm", "cum", "penis", "vagina",
            "breast", "nipple", "bikini", "lingerie", "strip", "cam", "webcam",
            
            # مصطلحات مشبوهة
            "onlyfans", "chaturbate", "pornhub", "xvideos", "redtube", "youporn",
            "escort", "prostitute", "hookup", "dating", "sugar daddy", "sugar baby",
            
            # رموز وإيموجي مشبوهة
            "🔞", "🍑", "🍆", "💦", "👙", "🔥💋", "😈", "🥵"
        ]
        
        # كلمات مشبوهة (تحتاج مراجعة)
        self.suspicious_keywords = [
            "حبيبي", "حبيبتي", "عزيزي", "عزيزتي", "جميل", "جميلة",
            "صور شخصية", "فيديو شخصي", "خاص", "سري", "مخفي",
            "تعارف", "صداقة", "علاقة", "حب", "رومانسي", "رومانسية",
            "beautiful", "gorgeous", "handsome", "cute", "lovely", "sweet",
            "private", "personal", "secret", "hidden", "exclusive"
        ]
        
        self.save_config()
        logger.info("✅ تم إنشاء إعدادات الفلترة الافتراضية")
    
    def save_config(self):
        """حفظ إعدادات الفلترة"""
        try:
            config = {
                "blocked_keywords": self.blocked_keywords,
                "suspicious_keywords": self.suspicious_keywords,
                "blocked_channels": list(self.blocked_channels),
                "whitelist_channels": list(self.whitelist_channels),
                "filter_stats": self.filter_stats,
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            logger.info("✅ تم حفظ إعدادات الفلترة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ إعدادات الفلترة: {e}")
    
    def check_text_content(self, text: str) -> FilterResult:
        """فحص النص للمحتوى غير المناسب"""
        if not text:
            return FilterResult(False, "", 0.0, [])
        
        text_lower = text.lower()
        matched_blocked = []
        matched_suspicious = []
        
        # فحص الكلمات المحظورة
        for keyword in self.blocked_keywords:
            if keyword.lower() in text_lower:
                matched_blocked.append(keyword)
        
        # فحص الكلمات المشبوهة
        for keyword in self.suspicious_keywords:
            if keyword.lower() in text_lower:
                matched_suspicious.append(keyword)
        
        # تحديد النتيجة
        if matched_blocked:
            confidence = min(1.0, len(matched_blocked) * 0.3 + 0.7)
            return FilterResult(
                is_blocked=True,
                reason=f"محتوى محظور: {', '.join(matched_blocked[:3])}",
                confidence=confidence,
                matched_keywords=matched_blocked
            )
        
        elif len(matched_suspicious) >= 2:
            confidence = min(0.8, len(matched_suspicious) * 0.2 + 0.4)
            return FilterResult(
                is_blocked=True,
                reason=f"محتوى مشبوه: {', '.join(matched_suspicious[:3])}",
                confidence=confidence,
                matched_keywords=matched_suspicious
            )
        
        return FilterResult(False, "", 0.0, [])
    
    def check_channel_content(self, channel_username: str) -> FilterResult:
        """فحص القناة للمحتوى غير المناسب"""
        clean_username = channel_username.replace("@", "").replace("https://t.me/", "").lower()
        
        # فحص القنوات المحظورة
        if clean_username in self.blocked_channels:
            return FilterResult(
                is_blocked=True,
                reason="قناة محظورة مسبقاً",
                confidence=1.0,
                matched_keywords=[]
            )
        
        # فحص القنوات المسموحة
        if clean_username in self.whitelist_channels:
            return FilterResult(False, "", 0.0, [])
        
        # فحص اسم القناة للكلمات المحظورة
        return self.check_text_content(clean_username)
    
    def add_blocked_channel(self, channel_username: str, reason: str = "محتوى غير مناسب"):
        """إضافة قناة للقائمة المحظورة"""
        clean_username = channel_username.replace("@", "").replace("https://t.me/", "").lower()
        self.blocked_channels.add(clean_username)
        self.filter_stats["blocked_channels"] += 1
        self.save_config()
        logger.info(f"✅ تم حظر القناة: {clean_username}")
    
    def remove_blocked_channel(self, channel_username: str):
        """إزالة قناة من القائمة المحظورة"""
        clean_username = channel_username.replace("@", "").replace("https://t.me/", "").lower()
        if clean_username in self.blocked_channels:
            self.blocked_channels.remove(clean_username)
            self.save_config()
            logger.info(f"✅ تم إلغاء حظر القناة: {clean_username}")
            return True
        return False
    
    def add_whitelist_channel(self, channel_username: str):
        """إضافة قناة للقائمة المسموحة"""
        clean_username = channel_username.replace("@", "").replace("https://t.me/", "").lower()
        self.whitelist_channels.add(clean_username)
        self.save_config()
        logger.info(f"✅ تم إضافة القناة للقائمة المسموحة: {clean_username}")
    
    def filter_message(self, message_text: str, channel_username: str = None) -> FilterResult:
        """فلترة رسالة كاملة"""
        self.filter_stats["total_checked"] += 1
        
        # فحص القناة أولاً
        if channel_username:
            channel_result = self.check_channel_content(channel_username)
            if channel_result.is_blocked:
                self.filter_stats["blocked_content"] += 1
                return channel_result
        
        # فحص محتوى الرسالة
        text_result = self.check_text_content(message_text)
        if text_result.is_blocked:
            self.filter_stats["blocked_content"] += 1
        
        return text_result
    
    def get_stats(self) -> Dict:
        """الحصول على إحصائيات الفلترة"""
        self.filter_stats["last_updated"] = datetime.now().isoformat()
        return self.filter_stats.copy()
    
    def add_keyword(self, keyword: str, is_blocked: bool = True):
        """إضافة كلمة جديدة للفلترة"""
        keyword = keyword.lower().strip()
        if is_blocked:
            if keyword not in self.blocked_keywords:
                self.blocked_keywords.append(keyword)
                logger.info(f"✅ تم إضافة كلمة محظورة: {keyword}")
        else:
            if keyword not in self.suspicious_keywords:
                self.suspicious_keywords.append(keyword)
                logger.info(f"✅ تم إضافة كلمة مشبوهة: {keyword}")
        
        self.save_config()
    
    def remove_keyword(self, keyword: str, is_blocked: bool = True):
        """إزالة كلمة من الفلترة"""
        keyword = keyword.lower().strip()
        try:
            if is_blocked and keyword in self.blocked_keywords:
                self.blocked_keywords.remove(keyword)
                logger.info(f"✅ تم إزالة كلمة محظورة: {keyword}")
            elif not is_blocked and keyword in self.suspicious_keywords:
                self.suspicious_keywords.remove(keyword)
                logger.info(f"✅ تم إزالة كلمة مشبوهة: {keyword}")
            
            self.save_config()
            return True
        except ValueError:
            return False

# إنشاء مثيل عام للفلتر
content_filter = ContentFilter()

if __name__ == "__main__":
    # اختبار النظام
    filter_system = ContentFilter()
    
    # اختبار النصوص
    test_texts = [
        "مرحبا كيف الحال؟",
        "صور ساخنة للكبار فقط",
        "فيديو جميل ومفيد",
        "محتوى إباحي فاضح",
        "تعارف وصداقة جميلة"
    ]
    
    print("🧪 اختبار نظام فلترة المحتوى:")
    print("=" * 50)
    
    for text in test_texts:
        result = filter_system.filter_message(text)
        status = "🚫 محظور" if result.is_blocked else "✅ مسموح"
        print(f"{status} | {text}")
        if result.is_blocked:
            print(f"   السبب: {result.reason}")
            print(f"   الثقة: {result.confidence:.2f}")
        print()
    
    # عرض الإحصائيات
    stats = filter_system.get_stats()
    print("📊 إحصائيات الفلترة:")
    print(f"   إجمالي المفحوص: {stats['total_checked']}")
    print(f"   المحتوى المحظور: {stats['blocked_content']}")
    print(f"   القنوات المحظورة: {stats['blocked_channels']}")
