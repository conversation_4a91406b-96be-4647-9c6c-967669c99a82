#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام اللغات المتعدد للبوت
يدعم العربية والإنجليزية مع إمكانية التبديل
"""

import json
import os
from typing import Dict, Any

class LanguageManager:
    """مدير اللغات للبوت"""
    
    def __init__(self):
        self.languages_file = "user_languages.json"
        self.user_languages = self.load_user_languages()
        self.default_language = "ar"  # العربية كلغة افتراضية
        
        # قاموس الترجمات
        self.translations = {
            "ar": {
                # الرسائل الأساسية
                "welcome": "🌟 مرحباً بك في بوت سحب المنشورات المتطور!",
                "help": "📋 قائمة الأوامر المتاحة:",
                "language_changed": "✅ تم تغيير اللغة بنجاح إلى العربية",
                "choose_language": "🌐 اختر اللغة المفضلة:",
                "current_language": "🗣️ اللغة الحالية: العربية",
                
                # أوامر البوت
                "start_desc": "بدء استخدام البوت",
                "help_desc": "عرض قائمة الأوامر",
                "language_desc": "تغيير لغة البوت",
                "settings_desc": "إعدادات البوت",
                "stats_desc": "إحصائيات الاستخدام",
                "vip_info_desc": "معلومات العضوية المميزة",
                
                # رسائل النظام
                "processing": "⏳ جاري المعالجة...",
                "error": "❌ حدث خطأ:",
                "success": "✅ تم بنجاح",
                "cancelled": "❌ تم الإلغاء",
                "invalid_url": "❌ رابط غير صحيح",
                "no_permission": "❌ ليس لديك صلاحية لهذا الأمر",
                
                # نظام VIP
                "vip_required": "💎 هذه الميزة متاحة للأعضاء المميزين فقط",
                "vip_info": "💎 معلومات العضوية المميزة",
                "vip_benefits": "🌟 مزايا العضوية المميزة:",
                "vip_price": "💰 السعر: 1$ شهرياً فقط",
                "vip_contact": "📞 للاشتراك تواصل مع:",
                
                # نظام الحظر
                "user_banned": "🚫 أنت محظور من استخدام البوت",
                "channel_banned": "🚫 هذه القناة محظورة",
                "ban_reason": "📝 السبب:",
                
                # نظام الفلترة
                "content_blocked": "🚫 تم حظر هذا المحتوى",
                "inappropriate_content": "📝 السبب: محتوى غير مناسب",
                "bot_policy": "⚖️ سياسة البوت: لا يسمح بالمحتوى غير المناسب",
                "content_protection": "💡 ملاحظة: البوت يحمي المستخدمين من المحتوى المخالف",
                
                # الأزرار
                "arabic": "🇸🇦 العربية",
                "english": "🇺🇸 English",
                "back": "🔙 رجوع",
                "cancel": "❌ إلغاء",
                "confirm": "✅ تأكيد",
                "next": "➡️ التالي",
                "previous": "⬅️ السابق",
                
                # إحصائيات
                "total_users": "👥 إجمالي المستخدمين:",
                "active_users": "🟢 المستخدمين النشطين:",
                "vip_users": "💎 الأعضاء المميزين:",
                "total_requests": "📊 إجمالي الطلبات:",
                "blocked_content": "🚫 المحتوى المحظور:",
                
                # أوامر الإدارة
                "admin_only": "👮‍♂️ هذا الأمر للمشرفين فقط",
                "user_id_required": "🆔 يرجى إدخال معرف المستخدم",
                "channel_name_required": "📺 يرجى إدخال اسم القناة",
                "reason_required": "📝 يرجى إدخال السبب",
                
                # رسائل الخطأ
                "unknown_error": "❌ خطأ غير معروف",
                "network_error": "🌐 خطأ في الاتصال",
                "file_not_found": "📁 الملف غير موجود",
                "permission_denied": "🔒 تم رفض الإذن",
                
                # رسائل النجاح
                "message_sent": "📤 تم إرسال الرسالة",
                "file_downloaded": "📥 تم تحميل الملف",
                "settings_saved": "💾 تم حفظ الإعدادات",
                "user_added": "👤 تم إضافة المستخدم",
                "channel_added": "📺 تم إضافة القناة"
            },
            
            "en": {
                # Basic messages
                "welcome": "🌟 Welcome to the Advanced Post Scraper Bot!",
                "help": "📋 Available Commands:",
                "language_changed": "✅ Language successfully changed to English",
                "choose_language": "🌐 Choose your preferred language:",
                "current_language": "🗣️ Current language: English",
                
                # Bot commands
                "start_desc": "Start using the bot",
                "help_desc": "Show commands list",
                "language_desc": "Change bot language",
                "settings_desc": "Bot settings",
                "stats_desc": "Usage statistics",
                "vip_info_desc": "VIP membership information",
                
                # System messages
                "processing": "⏳ Processing...",
                "error": "❌ An error occurred:",
                "success": "✅ Success",
                "cancelled": "❌ Cancelled",
                "invalid_url": "❌ Invalid URL",
                "no_permission": "❌ You don't have permission for this command",
                
                # VIP system
                "vip_required": "💎 This feature is available for VIP members only",
                "vip_info": "💎 VIP Membership Information",
                "vip_benefits": "🌟 VIP Membership Benefits:",
                "vip_price": "💰 Price: Only $1 per month",
                "vip_contact": "📞 To subscribe contact:",
                
                # Ban system
                "user_banned": "🚫 You are banned from using the bot",
                "channel_banned": "🚫 This channel is banned",
                "ban_reason": "📝 Reason:",
                
                # Filter system
                "content_blocked": "🚫 This content has been blocked",
                "inappropriate_content": "📝 Reason: Inappropriate content",
                "bot_policy": "⚖️ Bot Policy: Inappropriate content is not allowed",
                "content_protection": "💡 Note: The bot protects users from inappropriate content",
                
                # Buttons
                "arabic": "🇸🇦 العربية",
                "english": "🇺🇸 English",
                "back": "🔙 Back",
                "cancel": "❌ Cancel",
                "confirm": "✅ Confirm",
                "next": "➡️ Next",
                "previous": "⬅️ Previous",
                
                # Statistics
                "total_users": "👥 Total Users:",
                "active_users": "🟢 Active Users:",
                "vip_users": "💎 VIP Members:",
                "total_requests": "📊 Total Requests:",
                "blocked_content": "🚫 Blocked Content:",
                
                # Admin commands
                "admin_only": "👮‍♂️ This command is for admins only",
                "user_id_required": "🆔 Please enter user ID",
                "channel_name_required": "📺 Please enter channel name",
                "reason_required": "📝 Please enter reason",
                
                # Error messages
                "unknown_error": "❌ Unknown error",
                "network_error": "🌐 Network error",
                "file_not_found": "📁 File not found",
                "permission_denied": "🔒 Permission denied",
                
                # Success messages
                "message_sent": "📤 Message sent",
                "file_downloaded": "📥 File downloaded",
                "settings_saved": "💾 Settings saved",
                "user_added": "👤 User added",
                "channel_added": "📺 Channel added"
            }
        }
    
    def load_user_languages(self) -> Dict[int, str]:
        """تحميل لغات المستخدمين من الملف"""
        try:
            if os.path.exists(self.languages_file):
                with open(self.languages_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # تحويل المفاتيح من string إلى int (تجاهل المفاتيح غير الرقمية)
                    user_langs = {}
                    for k, v in data.items():
                        try:
                            user_langs[int(k)] = v
                        except ValueError:
                            # تجاهل المفاتيح غير الرقمية مثل "default_language"
                            continue
                    return user_langs
            return {}
        except Exception as e:
            print(f"خطأ في تحميل لغات المستخدمين: {e}")
            return {}
    
    def save_user_languages(self):
        """حفظ لغات المستخدمين في الملف"""
        try:
            # تحويل المفاتيح من int إلى string للحفظ في JSON
            data = {str(k): v for k, v in self.user_languages.items()}
            with open(self.languages_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ لغات المستخدمين: {e}")
    
    def get_user_language(self, user_id: int) -> str:
        """الحصول على لغة المستخدم"""
        return self.user_languages.get(user_id, self.default_language)
    
    def set_user_language(self, user_id: int, language: str):
        """تعيين لغة المستخدم"""
        if language in self.translations:
            self.user_languages[user_id] = language
            self.save_user_languages()
            return True
        return False
    
    def get_text(self, user_id: int, key: str, **kwargs) -> str:
        """الحصول على النص المترجم للمستخدم"""
        language = self.get_user_language(user_id)
        
        # الحصول على النص من القاموس
        text = self.translations.get(language, {}).get(key, key)
        
        # إذا لم يوجد النص في اللغة المحددة، استخدم العربية كاحتياطي
        if text == key and language != self.default_language:
            text = self.translations.get(self.default_language, {}).get(key, key)
        
        # تطبيق المتغيرات إذا وجدت
        if kwargs:
            try:
                text = text.format(**kwargs)
            except:
                pass
        
        return text
    
    def get_available_languages(self) -> Dict[str, str]:
        """الحصول على قائمة اللغات المتاحة"""
        return {
            "ar": "🇸🇦 العربية",
            "en": "🇺🇸 English"
        }
    
    def get_language_stats(self) -> Dict[str, int]:
        """إحصائيات استخدام اللغات"""
        stats = {}
        for lang in self.translations.keys():
            stats[lang] = sum(1 for user_lang in self.user_languages.values() if user_lang == lang)
        
        # إضافة المستخدمين الذين يستخدمون اللغة الافتراضية
        total_users = len(self.user_languages)
        default_users = sum(1 for lang in self.user_languages.values() if lang == self.default_language)
        
        return stats

# إنشاء مثيل عام من مدير اللغات
language_manager = LanguageManager()
